package com.taobao.wireless.orange.service.probe;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.taobao.wireless.orange.publish.probe.SwitchProbePublishService;
import com.taobao.wireless.orange.publish.probe.TextProbePublishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j(topic = "probe")
public class ProbePublishJob extends JavaProcessor {
    @Autowired
    private SwitchProbePublishService switchProbePublishService;

    @Autowired
    private TextProbePublishService textProbePublishService;

    @Override
    public ProcessResult process(JobContext context) {
        List<ProbeTask> tasks = List.of(
            new ProbeTask("Switch_probe_publish", switchProbePublishService::publish),
            new ProbeTask("Text_probe_publish", textProbePublishService::publish)
        );

        Optional<Throwable> lastException = tasks.stream()
            .map(this::executeTask)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .reduce((first, second) -> second);

        if (lastException.isPresent()) {
            log.error("Probe_generate error", lastException.get());
            return new ProcessResult(false, lastException.get().getMessage());
        }

        log.info("Probe_generate success");
        return new ProcessResult(true);
    }

    private Optional<Throwable> executeTask(ProbeTask task) {
        try {
            task.operation().run();
            return Optional.empty();
        } catch (Throwable e) {
            log.error("{} error", task.name(), e);
            return Optional.of(e);
        }
    }

    private record ProbeTask(String name, Runnable operation) {}

    @Override
    public void kill(JobContext context) {
        // 任务超时被中断
        log.error("Probe_generate job killed");
        super.kill(context);
    }
}
